{"auditReportVersion": 2, "vulnerabilities": {"@eslint/plugin-kit": {"name": "@eslint/plugin-kit", "severity": "low", "isDirect": false, "via": [{"source": 1100564, "name": "@eslint/plugin-kit", "dependency": "@eslint/plugin-kit", "title": "Regular Expression Denial of Service (ReDoS) in @eslint/plugin-kit", "url": "https://github.com/advisories/GHSA-7q7g-4xm8-89cq", "severity": "low", "cwe": ["CWE-770", "CWE-1333"], "cvss": {"score": 3.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:N/I:N/A:L"}, "range": "<0.2.3"}], "effects": [], "range": "<0.2.3", "nodes": ["Projet-RB2/Agent IA/node_modules/@eslint/plugin-kit", "Projet-RB2/Financial-Management/node_modules/@eslint/plugin-kit"], "fixAvailable": true}, "@nestjs/cli": {"name": "@nestjs/cli", "severity": "critical", "isDirect": true, "via": ["webpack"], "effects": [], "range": "7.5.1-next.1 || 7.5.2-next.2 - 9.2.0", "nodes": ["vimana/node_modules/@nestjs/cli"], "fixAvailable": {"name": "@nestjs/cli", "version": "11.0.7", "isSemVerMajor": true}}, "@nestjs/common": {"name": "@nestjs/common", "severity": "high", "isDirect": true, "via": [{"source": 1103903, "name": "@nestjs/common", "dependency": "@nestjs/common", "title": "nest allows a remote attacker to execute arbitrary code via the Content-Type header", "url": "https://github.com/advisories/GHSA-cj7v-w2c7-cp7c", "severity": "moderate", "cwe": ["CWE-94"], "cvss": {"score": 5.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:R/S:U/C:L/I:L/A:L"}, "range": "<10.4.16"}, "axios"], "effects": ["@nestjs/config", "@nestjs/core", "@nestjs/jwt", "@nestjs/mapped-types", "@nestjs/passport", "@nestjs/platform-express", "@nestjs/swagger", "@nestjs/testing"], "range": "<=10.4.15", "nodes": ["vimana/node_modules/@nestjs/common"], "fixAvailable": {"name": "@nestjs/common", "version": "11.1.2", "isSemVerMajor": true}}, "@nestjs/config": {"name": "@nestjs/config", "severity": "moderate", "isDirect": true, "via": ["@nestjs/common"], "effects": [], "range": "<=2.3.4", "nodes": ["vimana/node_modules/@nestjs/config"], "fixAvailable": {"name": "@nestjs/config", "version": "4.0.2", "isSemVerMajor": true}}, "@nestjs/core": {"name": "@nestjs/core", "severity": "high", "isDirect": true, "via": ["@nestjs/common", "@nestjs/platform-express", {"source": 1091325, "name": "@nestjs/core", "dependency": "@nestjs/core", "title": "@nestjs/core vulnerable to Information Exposure via StreamableFile pipe", "url": "https://github.com/advisories/GHSA-4jpv-8r57-pv7j", "severity": "moderate", "cwe": ["CWE-200"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:L/I:N/A:N"}, "range": "<9.0.5"}, "path-to-regexp"], "effects": ["@nestjs/platform-express", "@nestjs/swagger", "@nestjs/testing"], "range": "<=10.4.1", "nodes": ["vimana/node_modules/@nestjs/core"], "fixAvailable": {"name": "@nestjs/core", "version": "11.1.2", "isSemVerMajor": true}}, "@nestjs/jwt": {"name": "@nestjs/jwt", "severity": "moderate", "isDirect": true, "via": ["@nestjs/common", "jsonwebtoken"], "effects": [], "range": "<=10.0.3", "nodes": ["vimana/node_modules/@nestjs/jwt"], "fixAvailable": {"name": "@nestjs/jwt", "version": "11.0.0", "isSemVerMajor": true}}, "@nestjs/mapped-types": {"name": "@nestjs/mapped-types", "severity": "critical", "isDirect": false, "via": ["@nestjs/common", "class-validator"], "effects": ["@nestjs/swagger"], "range": "<=1.2.2", "nodes": ["vimana/node_modules/@nestjs/mapped-types"], "fixAvailable": {"name": "@nestjs/swagger", "version": "11.2.0", "isSemVerMajor": true}}, "@nestjs/passport": {"name": "@nestjs/passport", "severity": "moderate", "isDirect": true, "via": ["@nestjs/common"], "effects": [], "range": "<=9.0.3", "nodes": ["vimana/node_modules/@nestjs/passport"], "fixAvailable": {"name": "@nestjs/passport", "version": "11.0.5", "isSemVerMajor": true}}, "@nestjs/platform-express": {"name": "@nestjs/platform-express", "severity": "high", "isDirect": true, "via": ["@nestjs/common", "@nestjs/core", "body-parser", "express", "multer"], "effects": ["@nestjs/core", "@nestjs/testing"], "range": "<=10.4.17 || 11.0.0-next.1 - 11.1.1", "nodes": ["Projet-RB2/Backend-NestJS/node_modules/@nestjs/platform-express", "Projet-RB2/Security/node_modules/@nestjs/platform-express", "vimana/node_modules/@nestjs/platform-express"], "fixAvailable": {"name": "@nestjs/core", "version": "11.1.2", "isSemVerMajor": true}}, "@nestjs/swagger": {"name": "@nestjs/swagger", "severity": "high", "isDirect": true, "via": ["@nestjs/common", "@nestjs/core", "@nestjs/mapped-types", "path-to-regexp"], "effects": [], "range": "<=7.4.0", "nodes": ["vimana/node_modules/@nestjs/swagger"], "fixAvailable": {"name": "@nestjs/swagger", "version": "11.2.0", "isSemVerMajor": true}}, "@nestjs/testing": {"name": "@nestjs/testing", "severity": "high", "isDirect": true, "via": ["@nestjs/common", "@nestjs/core", "@nestjs/platform-express"], "effects": [], "range": "<=9.4.3", "nodes": ["vimana/node_modules/@nestjs/testing"], "fixAvailable": {"name": "@nestjs/testing", "version": "11.1.2", "isSemVerMajor": true}}, "@puppeteer/browsers": {"name": "@puppeteer/browsers", "severity": "high", "isDirect": false, "via": ["tar-fs"], "effects": ["puppeteer", "puppeteer-core"], "range": "1.4.2 - 2.2.3", "nodes": ["hanuman-unified/node_modules/@puppeteer/browsers"], "fixAvailable": {"name": "puppeteer", "version": "24.9.0", "isSemVerMajor": true}}, "@qdrant/js-client-rest": {"name": "@qdrant/js-client-rest", "severity": "low", "isDirect": true, "via": ["undici"], "effects": ["@qdrant/qdrant-js"], "range": "1.8.0 - 1.14.0", "nodes": ["Projet-RB2/Agent IA/node_modules/@qdrant/js-client-rest"], "fixAvailable": true}, "@qdrant/qdrant-js": {"name": "@qdrant/qdrant-js", "severity": "low", "isDirect": true, "via": ["@qdrant/js-client-rest"], "effects": [], "range": "1.8.0 - 1.14.0", "nodes": ["Projet-RB2/Agent IA/node_modules/@qdrant/qdrant-js"], "fixAvailable": true}, "@supabase/auth-js": {"name": "@supabase/auth-js", "severity": "low", "isDirect": false, "via": [{"source": 1105072, "name": "@supabase/auth-js", "dependency": "@supabase/auth-js", "title": "auth-js Vulnerable to Insecure Path Routing from Malformed User Input", "url": "https://github.com/advisories/GHSA-8r88-6cj9-9fh5", "severity": "low", "cwe": ["CWE-22"], "cvss": {"score": 0, "vectorString": null}, "range": "<2.69.1"}], "effects": ["@supabase/supabase-js"], "range": "<2.69.1", "nodes": ["Projet-RB2/Agent IA/node_modules/@supabase/auth-js"], "fixAvailable": true}, "@supabase/supabase-js": {"name": "@supabase/supabase-js", "severity": "low", "isDirect": true, "via": ["@supabase/auth-js"], "effects": [], "range": "2.41.1 - 2.49.2", "nodes": ["Projet-RB2/Agent IA/node_modules/@supabase/supabase-js"], "fixAvailable": true}, "@svgr/plugin-svgo": {"name": "@svgr/plugin-svgo", "severity": "high", "isDirect": false, "via": ["svgo"], "effects": ["@svgr/webpack"], "range": "<=5.5.0", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/@svgr/plugin-svgo"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "@svgr/webpack": {"name": "@svgr/webpack", "severity": "high", "isDirect": false, "via": ["@svgr/plugin-svgo"], "effects": ["react-scripts"], "range": "4.0.0 - 5.5.0", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/@svgr/webpack"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "axios": {"name": "axios", "severity": "high", "isDirect": true, "via": [{"source": 1097679, "name": "axios", "dependency": "axios", "title": "Axios Cross-Site Request Forgery Vulnerability", "url": "https://github.com/advisories/GHSA-wf5p-g6vw-rhxx", "severity": "moderate", "cwe": ["CWE-352"], "cvss": {"score": 6.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:U/C:H/I:N/A:N"}, "range": ">=0.8.1 <0.28.0"}, {"source": 1103617, "name": "axios", "dependency": "axios", "title": "axios Requests Vulnerable To Possible SSRF and Credential Leakage via Absolute URL", "url": "https://github.com/advisories/GHSA-jr5f-v2jv-69x6", "severity": "high", "cwe": ["CWE-918"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.30.0"}], "effects": ["@nestjs/common"], "range": "<=0.29.0", "nodes": ["vimana/node_modules/@nestjs/common/node_modules/axios", "vimana/node_modules/axios"], "fixAvailable": {"name": "axios", "version": "1.9.0", "isSemVerMajor": true}}, "body-parser": {"name": "body-parser", "severity": "high", "isDirect": false, "via": [{"source": 1099520, "name": "body-parser", "dependency": "body-parser", "title": "body-parser vulnerable to denial of service when url encoding is enabled", "url": "https://github.com/advisories/GHSA-qwcr-r2fm-qrc7", "severity": "high", "cwe": ["CWE-405"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<1.20.3"}], "effects": ["@nestjs/platform-express", "express"], "range": "<1.20.3", "nodes": ["Projet-RB2/Security/node_modules/body-parser", "vimana/node_modules/body-parser"], "fixAvailable": {"name": "@nestjs/platform-express", "version": "11.1.2", "isSemVerMajor": true}}, "class-validator": {"name": "class-validator", "severity": "critical", "isDirect": true, "via": [{"source": 1088816, "name": "class-validator", "dependency": "class-validator", "title": "SQL Injection and Cross-site Scripting in class-validator", "url": "https://github.com/advisories/GHSA-fj58-h2fr-3pp2", "severity": "critical", "cwe": ["CWE-79", "CWE-89"], "cvss": {"score": 9.8, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"}, "range": "<0.14.0"}], "effects": ["@nestjs/mapped-types"], "range": "<0.14.0", "nodes": ["vimana/node_modules/class-validator"], "fixAvailable": {"name": "class-validator", "version": "0.14.2", "isSemVerMajor": true}}, "cookie": {"name": "cookie", "severity": "low", "isDirect": false, "via": [{"source": 1103907, "name": "cookie", "dependency": "cookie", "title": "cookie accepts cookie name, path, and domain with out of bounds characters", "url": "https://github.com/advisories/GHSA-pxg6-pf52-xh8x", "severity": "low", "cwe": ["CWE-74"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.7.0"}], "effects": ["express"], "range": "<0.7.0", "nodes": ["Projet-RB2/Security/node_modules/cookie", "vimana/node_modules/@nestjs/platform-express/node_modules/cookie"], "fixAvailable": {"name": "express", "version": "4.21.2", "isSemVerMajor": false}}, "css-select": {"name": "css-select", "severity": "high", "isDirect": false, "via": ["nth-check"], "effects": ["svgo"], "range": "<=3.1.0", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/svgo/node_modules/css-select"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "esbuild": {"name": "esbuild", "severity": "moderate", "isDirect": false, "via": [{"source": 1102341, "name": "esbuild", "dependency": "esbuild", "title": "esbuild enables any website to send any requests to the development server and read the response", "url": "https://github.com/advisories/GHSA-67mh-4wv8-2f99", "severity": "moderate", "cwe": ["CWE-346"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:H/I:N/A:N"}, "range": "<=0.24.2"}], "effects": ["vite"], "range": "<=0.24.2", "nodes": ["node_modules/esbuild", "Projet-RB2/node_modules/vite/node_modules/esbuild"], "fixAvailable": {"name": "vite", "version": "6.3.5", "isSemVerMajor": true}}, "express": {"name": "express", "severity": "high", "isDirect": true, "via": [{"source": 1096820, "name": "express", "dependency": "express", "title": "Express.js Open Redirect in malformed URLs", "url": "https://github.com/advisories/GHSA-rv95-896h-c2vc", "severity": "moderate", "cwe": ["CWE-601", "CWE-1286"], "cvss": {"score": 6.1, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:L/I:L/A:N"}, "range": "<4.19.2"}, {"source": 1100530, "name": "express", "dependency": "express", "title": "express vulnerable to XSS via response.redirect()", "url": "https://github.com/advisories/GHSA-qw6h-vgh9-j6wx", "severity": "low", "cwe": ["CWE-79"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L"}, "range": "<4.20.0"}, "body-parser", "cookie", "path-to-regexp", "send", "serve-static"], "effects": [], "range": "<=4.21.1 || 5.0.0-alpha.1 - 5.0.0", "nodes": ["Projet-RB2/Security/node_modules/express", "vimana/node_modules/@nestjs/platform-express/node_modules/express"], "fixAvailable": {"name": "express", "version": "4.21.2", "isSemVerMajor": false}}, "formidable": {"name": "formidable", "severity": "low", "isDirect": false, "via": [{"source": 1105074, "name": "formidable", "dependency": "formidable", "title": "Formidable relies on hexoid to prevent guessing of filenames for untrusted executable content", "url": "https://github.com/advisories/GHSA-75v8-2h7p-7m2m", "severity": "low", "cwe": ["CWE-338"], "cvss": {"score": 3.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:L/A:N"}, "range": ">=2.1.0 <2.1.3"}], "effects": [], "range": "2.1.0 - 2.1.2", "nodes": ["Projet-RB2/Backend-NestJS/node_modules/formidable"], "fixAvailable": true}, "jsonwebtoken": {"name": "jsonwebtoken", "severity": "high", "isDirect": false, "via": [{"source": 1097690, "name": "jsonwebtoken", "dependency": "jsonwebtoken", "title": "jsonwebtoken unrestricted key type could lead to legacy keys usage ", "url": "https://github.com/advisories/GHSA-8cf7-32gw-wr33", "severity": "high", "cwe": ["CWE-327"], "cvss": {"score": 8.1, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:L/UI:N/S:U/C:H/I:H/A:N"}, "range": "<=8.5.1"}, {"source": 1097694, "name": "jsonwebtoken", "dependency": "jsonwebtoken", "title": "jsonwebtoken's insecure implementation of key retrieval function could lead to Forgeable Public/Private Tokens from RSA to HMAC", "url": "https://github.com/advisories/GHSA-hjrf-2m68-5959", "severity": "moderate", "cwe": ["CWE-287", "CWE-1259"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:L"}, "range": "<=8.5.1"}, {"source": 1102458, "name": "jsonwebtoken", "dependency": "jsonwebtoken", "title": "jsonwebtoken vulnerable to signature validation bypass due to insecure default algorithm in jwt.verify()", "url": "https://github.com/advisories/GHSA-qwph-4952-7xr6", "severity": "moderate", "cwe": ["CWE-287", "CWE-327", "CWE-347"], "cvss": {"score": 6.4, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:H/A:L"}, "range": "<9.0.0"}], "effects": ["@nestjs/jwt"], "range": "<=8.5.1", "nodes": ["vimana/node_modules/jsonwebtoken"], "fixAvailable": {"name": "@nestjs/jwt", "version": "11.0.0", "isSemVerMajor": true}}, "lint-staged": {"name": "lint-staged", "severity": "moderate", "isDirect": true, "via": ["micromatch"], "effects": [], "range": "7.0.0 - 8.2.1 || 13.3.0 - 15.2.4", "nodes": ["hanuman-unified/node_modules/lint-staged"], "fixAvailable": true}, "micromatch": {"name": "micromatch", "severity": "moderate", "isDirect": false, "via": [{"source": 1098681, "name": "micromatch", "dependency": "micromatch", "title": "Regular Expression Denial of Service (ReDoS) in micromatch", "url": "https://github.com/advisories/GHSA-952p-6rrq-rcjv", "severity": "moderate", "cwe": ["CWE-1333"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:L"}, "range": "<4.0.8"}], "effects": ["lint-staged"], "range": "<4.0.8", "nodes": ["hanuman-unified/node_modules/lint-staged/node_modules/micromatch"], "fixAvailable": true}, "multer": {"name": "multer", "severity": "high", "isDirect": true, "via": [{"source": 1104780, "name": "multer", "dependency": "multer", "title": "Multer vulnerable to Denial of Service from maliciously crafted requests", "url": "https://github.com/advisories/GHSA-4pg4-qvpc-4q3h", "severity": "high", "cwe": ["CWE-248"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": ">=1.4.4-lts.1 <2.0.0"}], "effects": ["@nestjs/platform-express"], "range": ">=1.4.4-lts.1 <2.0.0", "nodes": ["hanuman-unified/node_modules/multer", "Projet-RB2/Backend-NestJS/node_modules/multer", "Projet-RB2/Security/node_modules/@nestjs/platform-express/node_modules/multer", "Projet-RB2/Security/node_modules/multer", "vimana/node_modules/multer"], "fixAvailable": {"name": "multer", "version": "2.0.0", "isSemVerMajor": true}}, "nth-check": {"name": "nth-check", "severity": "high", "isDirect": false, "via": [{"source": 1095141, "name": "nth-check", "dependency": "nth-check", "title": "Inefficient Regular Expression Complexity in nth-check", "url": "https://github.com/advisories/GHSA-rp65-9cf3-cjxr", "severity": "high", "cwe": ["CWE-1333"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<2.0.1"}], "effects": ["css-select"], "range": "<2.0.1", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/svgo/node_modules/nth-check"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "passport": {"name": "passport", "severity": "moderate", "isDirect": true, "via": [{"source": 1093639, "name": "passport", "dependency": "passport", "title": "Passport vulnerable to session regeneration when a users logs in or out", "url": "https://github.com/advisories/GHSA-v923-w3x8-wh69", "severity": "moderate", "cwe": ["CWE-384"], "cvss": {"score": 4.8, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:N/S:U/C:L/I:N/A:L"}, "range": "<0.6.0"}], "effects": [], "range": "<0.6.0", "nodes": ["vimana/node_modules/passport"], "fixAvailable": {"name": "passport", "version": "0.7.0", "isSemVerMajor": true}}, "path-to-regexp": {"name": "path-to-regexp", "severity": "high", "isDirect": false, "via": [{"source": 1101844, "name": "path-to-regexp", "dependency": "path-to-regexp", "title": "Unpatched `path-to-regexp` ReDoS in 0.1.x", "url": "https://github.com/advisories/GHSA-rhx6-c78j-4q9w", "severity": "high", "cwe": ["CWE-1333"], "cvss": {"score": 0, "vectorString": null}, "range": "<0.1.12"}, {"source": 1101848, "name": "path-to-regexp", "dependency": "path-to-regexp", "title": "path-to-regexp outputs backtracking regular expressions", "url": "https://github.com/advisories/GHSA-9wv6-86v2-598j", "severity": "high", "cwe": ["CWE-1333"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": ">=2.0.0 <3.3.0"}, {"source": 1101850, "name": "path-to-regexp", "dependency": "path-to-regexp", "title": "path-to-regexp outputs backtracking regular expressions", "url": "https://github.com/advisories/GHSA-9wv6-86v2-598j", "severity": "high", "cwe": ["CWE-1333"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": "<0.1.10"}], "effects": ["@nestjs/core", "@nestjs/swagger", "express"], "range": "<=0.1.11 || 2.0.0 - 3.2.0", "nodes": ["Projet-RB2/Security/node_modules/path-to-regexp", "vimana/node_modules/@nestjs/platform-express/node_modules/path-to-regexp", "vimana/node_modules/path-to-regexp"], "fixAvailable": {"name": "@nestjs/core", "version": "11.1.2", "isSemVerMajor": true}}, "postcss": {"name": "postcss", "severity": "moderate", "isDirect": false, "via": [{"source": 1094544, "name": "postcss", "dependency": "postcss", "title": "PostCSS line return parsing error", "url": "https://github.com/advisories/GHSA-7fh5-64p2-3v2j", "severity": "moderate", "cwe": ["CWE-74", "CWE-144"], "cvss": {"score": 5.3, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:L/A:N"}, "range": "<8.4.31"}], "effects": ["resolve-url-loader"], "range": "<8.4.31", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/resolve-url-loader/node_modules/postcss"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "puppeteer": {"name": "puppeteer", "severity": "high", "isDirect": true, "via": ["@puppeteer/browsers", "puppeteer-core"], "effects": [], "range": "18.2.0 - 22.13.0", "nodes": ["hanuman-unified/node_modules/puppeteer"], "fixAvailable": {"name": "puppeteer", "version": "24.9.0", "isSemVerMajor": true}}, "puppeteer-core": {"name": "puppeteer-core", "severity": "high", "isDirect": false, "via": ["@puppeteer/browsers", "ws"], "effects": ["puppeteer"], "range": "11.0.0 - 22.13.0", "nodes": ["hanuman-unified/node_modules/puppeteer-core"], "fixAvailable": {"name": "puppeteer", "version": "24.9.0", "isSemVerMajor": true}}, "react-scripts": {"name": "react-scripts", "severity": "high", "isDirect": true, "via": ["@svgr/webpack", "resolve-url-loader"], "effects": [], "range": ">=2.1.4", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/react-scripts"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "resolve-url-loader": {"name": "resolve-url-loader", "severity": "moderate", "isDirect": false, "via": ["postcss"], "effects": ["react-scripts"], "range": "0.0.1-experiment-postcss || 3.0.0-alpha.1 - 4.0.0", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/resolve-url-loader"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "send": {"name": "send", "severity": "low", "isDirect": false, "via": [{"source": 1100526, "name": "send", "dependency": "send", "title": "send vulnerable to template injection that can lead to XSS", "url": "https://github.com/advisories/GHSA-m6fv-jmcg-4jfg", "severity": "low", "cwe": ["CWE-79"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L"}, "range": "<0.19.0"}], "effects": ["express", "serve-static"], "range": "<0.19.0", "nodes": ["Projet-RB2/Security/node_modules/send", "vimana/node_modules/@nestjs/platform-express/node_modules/send"], "fixAvailable": {"name": "express", "version": "4.21.2", "isSemVerMajor": false}}, "serve-static": {"name": "serve-static", "severity": "low", "isDirect": false, "via": [{"source": 1100528, "name": "serve-static", "dependency": "serve-static", "title": "serve-static vulnerable to template injection that can lead to XSS", "url": "https://github.com/advisories/GHSA-cm22-4g7w-348p", "severity": "low", "cwe": ["CWE-79"], "cvss": {"score": 5, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:N/UI:R/S:U/C:L/I:L/A:L"}, "range": "<1.16.0"}, "send"], "effects": ["express"], "range": "<=1.16.0", "nodes": ["Projet-RB2/Security/node_modules/serve-static", "vimana/node_modules/@nestjs/platform-express/node_modules/serve-static"], "fixAvailable": {"name": "express", "version": "4.21.2", "isSemVerMajor": false}}, "svgo": {"name": "svgo", "severity": "high", "isDirect": false, "via": ["css-select"], "effects": ["@svgr/plugin-svgo"], "range": "1.0.0 - 1.3.2", "nodes": ["Projet-RB2/Front-Audrey-V1-Main-main/node_modules/svgo"], "fixAvailable": {"name": "react-scripts", "version": "3.0.1", "isSemVerMajor": true}}, "tar-fs": {"name": "tar-fs", "severity": "high", "isDirect": false, "via": [{"source": 1104676, "name": "tar-fs", "dependency": "tar-fs", "title": "tar-fs Vulnerable to Link Following and Path Traversal via Extracting a Crafted tar File", "url": "https://github.com/advisories/GHSA-pq67-2wwv-3xjx", "severity": "high", "cwe": ["CWE-22"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:H/A:N"}, "range": ">=3.0.0 <3.0.7"}], "effects": ["@puppeteer/browsers"], "range": "3.0.0 - 3.0.6", "nodes": ["hanuman-unified/node_modules/tar-fs"], "fixAvailable": {"name": "puppeteer", "version": "24.9.0", "isSemVerMajor": true}}, "undici": {"name": "undici", "severity": "low", "isDirect": false, "via": [{"source": 1104501, "name": "undici", "dependency": "undici", "title": "undici Denial of Service attack via bad certificate data", "url": "https://github.com/advisories/GHSA-cxrh-j4jr-qwg3", "severity": "low", "cwe": ["CWE-401"], "cvss": {"score": 3.1, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:N/I:N/A:L"}, "range": "<5.29.0"}], "effects": ["@qdrant/js-client-rest"], "range": "<5.29.0", "nodes": ["Projet-RB2/Agent IA/node_modules/undici"], "fixAvailable": true}, "vite": {"name": "vite", "severity": "moderate", "isDirect": true, "via": ["esbuild"], "effects": ["vite-node", "vitest"], "range": "0.11.0 - 6.1.6", "nodes": ["node_modules/vite", "Projet-RB2/Financial-Management/node_modules/vite", "Projet-RB2/node_modules/vite"], "fixAvailable": {"name": "vite", "version": "6.3.5", "isSemVerMajor": true}}, "vite-node": {"name": "vite-node", "severity": "moderate", "isDirect": false, "via": ["vite"], "effects": ["vitest"], "range": "<=2.2.0-beta.2", "nodes": ["node_modules/vite-node", "Projet-RB2/Security/node_modules/vite-node"], "fixAvailable": {"name": "vitest", "version": "3.1.4", "isSemVerMajor": true}}, "vitest": {"name": "vitest", "severity": "critical", "isDirect": true, "via": [{"source": 1102430, "name": "vitest", "dependency": "vitest", "title": "Vitest allows Remote Code Execution when accessing a malicious website while Vitest API server is listening", "url": "https://github.com/advisories/GHSA-9crc-q9x8-hgqq", "severity": "critical", "cwe": ["CWE-1385"], "cvss": {"score": 9.7, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:R/S:C/C:H/I:H/A:H"}, "range": ">=1.0.0 <1.6.1"}, "vite", "vite-node"], "effects": [], "range": "0.0.1 - 0.0.12 || 0.0.29 - 0.0.122 || 0.3.3 - 2.2.0-beta.2", "nodes": ["node_modules/vitest", "Projet-RB2/Security/node_modules/vitest"], "fixAvailable": {"name": "vitest", "version": "3.1.4", "isSemVerMajor": true}}, "webpack": {"name": "webpack", "severity": "critical", "isDirect": false, "via": [{"source": 1094471, "name": "webpack", "dependency": "webpack", "title": "Cross-realm object access in Webpack 5", "url": "https://github.com/advisories/GHSA-hc6q-2mpp-qw7j", "severity": "critical", "cwe": [], "cvss": {"score": 9.8, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:H/I:H/A:H"}, "range": ">=5.0.0 <5.76.0"}, {"source": 1099351, "name": "webpack", "dependency": "webpack", "title": "Webpack's AutoPublicPathRuntimeModule has a DOM Clobbering Gadget that leads to XSS", "url": "https://github.com/advisories/GHSA-4vvj-4cpr-p986", "severity": "moderate", "cwe": ["CWE-79"], "cvss": {"score": 6.4, "vectorString": "CVSS:3.1/AV:N/AC:H/PR:L/UI:N/S:U/C:L/I:L/A:H"}, "range": ">=5.0.0-alpha.0 <5.94.0"}], "effects": ["@nestjs/cli"], "range": "5.0.0-alpha.0 - 5.93.0", "nodes": ["vimana/node_modules/@nestjs/cli/node_modules/webpack"], "fixAvailable": {"name": "@nestjs/cli", "version": "11.0.7", "isSemVerMajor": true}}, "ws": {"name": "ws", "severity": "high", "isDirect": false, "via": [{"source": 1098392, "name": "ws", "dependency": "ws", "title": "ws affected by a DoS when handling a request with many HTTP headers", "url": "https://github.com/advisories/GHSA-3h5v-q93c-6h6q", "severity": "high", "cwe": ["CWE-476"], "cvss": {"score": 7.5, "vectorString": "CVSS:3.1/AV:N/AC:L/PR:N/UI:N/S:U/C:N/I:N/A:H"}, "range": ">=8.0.0 <8.17.1"}], "effects": ["puppeteer-core"], "range": "8.0.0 - 8.17.0", "nodes": ["hanuman-unified/node_modules/puppeteer-core/node_modules/ws"], "fixAvailable": {"name": "puppeteer", "version": "24.9.0", "isSemVerMajor": true}}}, "metadata": {"vulnerabilities": {"info": 0, "low": 10, "moderate": 11, "high": 22, "critical": 5, "total": 48}, "dependencies": {"prod": 3013, "dev": 1954, "optional": 179, "peer": 42, "peerOptional": 0, "total": 5063}}}