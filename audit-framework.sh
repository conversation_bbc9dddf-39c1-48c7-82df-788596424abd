#!/bin/bash

# Script d'audit automatique pour Agentic-Coding-Framework-RB2
# Date: 28 Mai 2025
# Version: 1.0.0

echo "=================================================="
echo "🔍 AUDIT AUTOMATIQUE - Agentic-Coding-Framework-RB2"
echo "=================================================="
echo ""

# Variables
PROJECT_ROOT=$(pwd)
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
AUDIT_DIR="audit-results-$TIMESTAMP"
mkdir -p "$AUDIT_DIR"

# Fonction pour afficher les résultats
show_result() {
    if [ $1 -eq 0 ]; then
        echo "✅ $2"
    else
        echo "❌ $2"
    fi
}

# 1. Vérification de la structure du projet
echo "📁 Vérification de la structure du projet..."
REQUIRED_DIRS=("hanuman-unified" "Projet-RB2" "k8s" "scripts" "vimana")
for dir in "${REQUIRED_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        show_result 0 "Dossier $dir présent"
    else
        show_result 1 "Dossier $dir manquant"
    fi
done
echo ""

# 2. Analyse des dépendances
echo "📦 Analyse des dépendances..."
if [ -f "package.json" ]; then
    echo "Audit npm des vulnérabilités..."
    npm audit --json > "$AUDIT_DIR/npm-audit.json" 2>/dev/null
    VULNERABILITIES=$(cat "$AUDIT_DIR/npm-audit.json" | grep -c "high\|critical" || echo "0")
    if [ "$VULNERABILITIES" -gt 0 ]; then
        show_result 1 "Vulnérabilités trouvées: $VULNERABILITIES"
    else
        show_result 0 "Aucune vulnérabilité critique"
    fi
fi
echo ""

# 3. Vérification des fichiers sensibles
echo "🔐 Vérification des fichiers sensibles..."
SENSITIVE_PATTERNS=("password=" "api_key=" "secret=" "token=" "private_key=")
FOUND_SECRETS=0
for pattern in "${SENSITIVE_PATTERNS[@]}"; do
    RESULTS=$(grep -r "$pattern" --include="*.js" --include="*.ts" --include="*.jsx" --include="*.tsx" --exclude-dir=node_modules . 2>/dev/null | wc -l)
    if [ "$RESULTS" -gt 0 ]; then
        FOUND_SECRETS=$((FOUND_SECRETS + RESULTS))
    fi
done
if [ "$FOUND_SECRETS" -gt 0 ]; then
    show_result 1 "Secrets potentiels trouvés: $FOUND_SECRETS"
else
    show_result 0 "Aucun secret exposé détecté"
fi
echo ""

# 4. Analyse de la qualité du code
echo "📊 Analyse de la qualité du code..."
# Compter les fichiers TypeScript
TS_FILES=$(find . -name "*.ts" -o -name "*.tsx" -not -path "*/node_modules/*" | wc -l)
echo "   Fichiers TypeScript: $TS_FILES"

# Compter les fichiers de test
TEST_FILES=$(find . -name "*.test.ts" -o -name "*.test.tsx" -o -name "*.spec.ts" -o -name "*.spec.tsx" -not -path "*/node_modules/*" | wc -l)
echo "   Fichiers de test: $TEST_FILES"

# Calculer le ratio
if [ "$TS_FILES" -gt 0 ]; then
    TEST_RATIO=$((TEST_FILES * 100 / TS_FILES))
    echo "   Ratio de tests: $TEST_RATIO%"
    if [ "$TEST_RATIO" -lt 30 ]; then
        show_result 1 "Couverture de tests insuffisante"
    else
        show_result 0 "Couverture de tests acceptable"
    fi
fi
echo ""

# 5. Vérification de la documentation
echo "📚 Vérification de la documentation..."
README_COUNT=$(find . -name "README.md" -not -path "*/node_modules/*" | wc -l)
MD_COUNT=$(find . -name "*.md" -not -path "*/node_modules/*" | wc -l)
echo "   Fichiers README: $README_COUNT"
echo "   Total fichiers documentation: $MD_COUNT"
if [ "$MD_COUNT" -gt 20 ]; then
    show_result 0 "Documentation abondante"
else
    show_result 1 "Documentation insuffisante"
fi
echo ""

# 6. Analyse des conteneurs Docker
echo "🐳 Analyse des configurations Docker..."
DOCKER_FILES=$(find . -name "Dockerfile" -o -name "docker-compose*.yml" -not -path "*/node_modules/*" | wc -l)
echo "   Fichiers Docker trouvés: $DOCKER_FILES"
if [ "$DOCKER_FILES" -gt 0 ]; then
    show_result 0 "Configuration Docker présente"
else
    show_result 1 "Configuration Docker manquante"
fi
echo ""

# 7. Vérification Kubernetes
echo "☸️  Vérification des configurations Kubernetes..."
K8S_FILES=$(find . -name "*.yaml" -o -name "*.yml" | grep -E "(k8s|kubernetes)" | wc -l)
echo "   Fichiers Kubernetes trouvés: $K8S_FILES"
if [ "$K8S_FILES" -gt 0 ]; then
    show_result 0 "Configuration Kubernetes présente"
else
    show_result 1 "Configuration Kubernetes manquante"
fi
echo ""

# 8. Analyse de la taille du projet
echo "📏 Analyse de la taille du projet..."
TOTAL_LINES=$(find . -name "*.ts" -o -name "*.tsx" -o -name "*.js" -o -name "*.jsx" -not -path "*/node_modules/*" -exec wc -l {} + | tail -1 | awk '{print $1}')
echo "   Total lignes de code: $TOTAL_LINES"
TOTAL_FILES=$(find . -type f -not -path "*/node_modules/*" -not -path "*/.git/*" | wc -l)
echo "   Total fichiers: $TOTAL_FILES"
echo ""

# 9. Génération du rapport
echo "📄 Génération du rapport..."
cat > "$AUDIT_DIR/audit-report.txt" << EOF
RAPPORT D'AUDIT - Agentic-Coding-Framework-RB2
Date: $(date)
================================================

RÉSUMÉ EXÉCUTIF
--------------
Projet: Agentic-Coding-Framework-RB2
Version: 3.8.1
Lignes de code: $TOTAL_LINES
Fichiers totaux: $TOTAL_FILES
Fichiers TypeScript: $TS_FILES
Fichiers de test: $TEST_FILES
Documentation: $MD_COUNT fichiers

SÉCURITÉ
--------
Vulnérabilités npm: $VULNERABILITIES
Secrets potentiels: $FOUND_SECRETS

INFRASTRUCTURE
-------------
Fichiers Docker: $DOCKER_FILES
Fichiers Kubernetes: $K8S_FILES

RECOMMANDATIONS
--------------
EOF

if [ "$VULNERABILITIES" -gt 0 ]; then
    echo "- Corriger les vulnérabilités npm identifiées" >> "$AUDIT_DIR/audit-report.txt"
fi
if [ "$FOUND_SECRETS" -gt 0 ]; then
    echo "- Retirer les secrets du code source" >> "$AUDIT_DIR/audit-report.txt"
fi
if [ "$TEST_RATIO" -lt 30 ]; then
    echo "- Augmenter la couverture de tests" >> "$AUDIT_DIR/audit-report.txt"
fi

echo ""
echo "✅ Audit terminé! Résultats dans: $AUDIT_DIR/"
echo ""
echo "📊 Résumé:"
echo "   - Vulnérabilités: $VULNERABILITIES"
echo "   - Secrets exposés: $FOUND_SECRETS"
echo "   - Ratio de tests: $TEST_RATIO%"
echo "   - Documentation: $MD_COUNT fichiers"
echo ""

# Rendre le script exécutable
chmod +x "$0"