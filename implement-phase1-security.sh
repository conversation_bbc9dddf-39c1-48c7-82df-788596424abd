#!/bin/bash

# Script d'implémentation Phase 1 - Sécurité Urgente
# Pour Agentic-Coding-Framework-RB2
# Date: 29 Mai 2025

set -e

echo "🚨 PHASE 1 - IMPLÉMENTATION SÉCURITÉ URGENTE"
echo "==========================================="
echo ""

# Configuration
PROJECT_ROOT=$(pwd)
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
SECURITY_DIR="security-fixes-$TIMESTAMP"
LOG_FILE="$SECURITY_DIR/security-fixes.log"

# Créer le répertoire de travail
mkdir -p "$SECURITY_DIR"

# Fonction de logging
log() {
    echo "[$(date +'%Y-%m-%d %H:%M:%S')] $1" | tee -a "$LOG_FILE"
}

# Fonction de vérification
check_status() {
    if [ $? -eq 0 ]; then
        log "✅ $1 - Succès"
    else
        log "❌ $1 - Échec"
        exit 1
    fi
}

# 1. CORRECTION DES VULNÉRABILITÉS NPM
echo "📦 1. Correction des vulnérabilités npm..."
log "Début de l'audit npm"

# Sauvegarder l'état actuel
cp package.json "$SECURITY_DIR/package.json.backup"
cp package-lock.json "$SECURITY_DIR/package-lock.json.backup" 2>/dev/null || true

# Exécuter l'audit
npm audit --json > "$SECURITY_DIR/npm-audit-before.json"
log "Audit initial sauvegardé"

# Tenter de corriger automatiquement
npm audit fix --force 2>&1 | tee -a "$LOG_FILE"
check_status "Correction automatique npm"

# Mettre à jour les dépendances critiques
npm update lodash axios 2>&1 | tee -a "$LOG_FILE"
check_status "Mise à jour dépendances critiques"

# Nouvel audit
npm audit --json > "$SECURITY_DIR/npm-audit-after.json"
log "Audit post-correction sauvegardé"

echo ""
echo "📦 2. Recherche et migration des secrets..."
log "Début de la recherche de secrets"

# Créer le fichier .env.example
cat > .env.example << EOF
# Configuration Application
NODE_ENV=development
PORT=3000

# Base de données
DATABASE_URL=postgresql://user:password@localhost:5432/dbname

# JWT
JWT_SECRET=your-jwt-secret-here
JWT_EXPIRATION=7d

# API Keys (à remplacer)
GOOGLE_API_KEY=your-google-api-key
STRIPE_API_KEY=your-stripe-api-key
SENDGRID_API_KEY=your-sendgrid-api-key

# Redis
REDIS_URL=redis://localhost:6379

# AWS
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key
AWS_REGION=us-east-1

# Monitoring
SENTRY_DSN=your-sentry-dsn
EOF

log "Fichier .env.example créé"

# Scanner les fichiers pour les secrets
echo "🔍 Scan des secrets dans le code..."
SECRETS_FOUND=0

# Patterns de secrets à rechercher
declare -a PATTERNS=(
    "password.*=.*['\"].*['\"]"
    "api_key.*=.*['\"].*['\"]"
    "apiKey.*=.*['\"].*['\"]"
    "secret.*=.*['\"].*['\"]"
    "token.*=.*['\"].*['\"]"
    "private_key.*=.*['\"].*['\"]"
)

# Scanner et sauvegarder les résultats
> "$SECURITY_DIR/secrets-found.txt"
for pattern in "${PATTERNS[@]}"; do
    grep -r -E "$pattern" --include="*.js" --include="*.ts" --include="*.jsx" --include="*.tsx" \
        --exclude-dir=node_modules --exclude-dir=.git . >> "$SECURITY_DIR/secrets-found.txt" 2>/dev/null || true
done

SECRETS_FOUND=$(wc -l < "$SECURITY_DIR/secrets-found.txt")
log "Nombre de secrets potentiels trouvés: $SECRETS_FOUND"

echo ""
echo "🔒 3. Configuration Vault (simulation)..."
log "Configuration HashiCorp Vault"

# Créer la configuration Vault
mkdir -p "$SECURITY_DIR/vault-config"
cat > "$SECURITY_DIR/vault-config/vault-setup.sh" << 'EOF'
#!/bin/bash
# Script de configuration Vault

# 1. Installer Vault (si nécessaire)
# wget https://releases.hashicorp.com/vault/1.13.0/vault_1.13.0_linux_amd64.zip

# 2. Démarrer Vault en mode dev (pour test)
# vault server -dev

# 3. Configuration des secrets
export VAULT_ADDR='http://127.0.0.1:8200'

# Créer les secrets
vault kv put secret/app \
    jwt_secret="$(openssl rand -base64 32)" \
    database_url="postgresql://user:password@localhost:5432/dbname" \
    redis_url="redis://localhost:6379"

vault kv put secret/api-keys \
    google="your-google-api-key" \
    stripe="your-stripe-api-key" \
    sendgrid="your-sendgrid-api-key"

echo "Vault configuré avec succès!"
EOF

chmod +x "$SECURITY_DIR/vault-config/vault-setup.sh"
log "Script de configuration Vault créé"

echo ""
echo "🛡️ 4. Headers de sécurité..."
log "Configuration headers de sécurité"

# Créer la configuration nginx sécurisée
cat > "$SECURITY_DIR/nginx-security.conf" << 'EOF'
# Headers de sécurité
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-Content-Type-Options "nosniff" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header Referrer-Policy "strict-origin-when-cross-origin" always;
add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';" always;
add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
add_header Permissions-Policy "geolocation=(), microphone=(), camera=()" always;

# Désactiver les méthodes dangereuses
if ($request_method !~ ^(GET|HEAD|POST|PUT|DELETE|OPTIONS)$) {
    return 405;
}

# Limiter la taille des requêtes
client_max_body_size 10M;
client_body_buffer_size 128k;

# Timeout de sécurité
client_body_timeout 10;
client_header_timeout 10;
keepalive_timeout 5 5;
send_timeout 10;
EOF

log "Configuration nginx sécurisée créée"

echo ""
echo "🔧 5. Correction SQL Injection..."
log "Recherche de vulnérabilités SQL"

# Créer un exemple de correction
cat > "$SECURITY_DIR/sql-injection-fix-example.ts" << 'EOF'
// AVANT (Vulnérable)
const query = `SELECT * FROM users WHERE id = ${userId}`;

// APRÈS (Sécurisé avec Prisma)
const user = await prisma.user.findUnique({
  where: { id: userId }
});

// OU avec requête paramétrée
const query = 'SELECT * FROM users WHERE id = $1';
const result = await db.query(query, [userId]);
EOF

log "Exemple de correction SQL créé"

echo ""
echo "📊 RAPPORT DE SÉCURITÉ"
echo "===================="
echo ""

# Générer le rapport
cat > "$SECURITY_DIR/security-report.md" << EOF
# Rapport de Sécurité - Phase 1
Date: $(date)

## Résumé
- Vulnérabilités npm avant: $(cat "$SECURITY_DIR/npm-audit-before.json" | grep -c "high\|critical" || echo "0")
- Vulnérabilités npm après: $(cat "$SECURITY_DIR/npm-audit-after.json" | grep -c "high\|critical" || echo "0")
- Secrets trouvés: $SECRETS_FOUND
- Fichiers de configuration créés: 5

## Actions réalisées
1. ✅ Audit et correction npm
2. ✅ Scan des secrets
3. ✅ Configuration Vault préparée
4. ✅ Headers de sécurité nginx
5. ✅ Exemples de correction SQL

## Prochaines étapes
1. Migrer tous les secrets vers Vault
2. Appliquer les headers de sécurité
3. Corriger les injections SQL
4. Mettre en place le WAF
5. Configurer HTTPS/TLS

## Fichiers créés
- npm-audit-before.json
- npm-audit-after.json
- secrets-found.txt
- vault-config/
- nginx-security.conf
- sql-injection-fix-example.ts
EOF

log "Rapport de sécurité généré"

echo ""
echo "✅ Phase 1 - Script terminé!"
echo "📁 Résultats dans: $SECURITY_DIR/"
echo ""
echo "⚠️  ACTIONS MANUELLES REQUISES:"
echo "1. Examiner $SECURITY_DIR/secrets-found.txt"
echo "2. Migrer les secrets vers Vault ou .env"
echo "3. Appliquer nginx-security.conf"
echo "4. Corriger les vulnérabilités SQL"
echo ""

# Afficher le résumé
echo "📊 Résumé:"
VULN_BEFORE=$(cat "$SECURITY_DIR/npm-audit-before.json" | grep -c "high\|critical" || echo "0")
VULN_AFTER=$(cat "$SECURITY_DIR/npm-audit-after.json" | grep -c "high\|critical" || echo "0")
echo "   Vulnérabilités npm: $VULN_BEFORE → $VULN_AFTER"
echo "   Secrets à migrer: $SECRETS_FOUND"
echo ""